#!/usr/bin/env python3
"""
Test script to run classification with debug logging enabled
"""

import sys
import os
import logging
import asyncio
from unittest.mock import Mock

# Add project root to the python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../"))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging to see debug output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_classification_with_debug():
    """Test classification function with debug logging"""
    
    print("=== Testing Classification with Debug Logging ===\n")
    
    try:
        # Import required modules
        from app.llm.classification import get_document_types_with_bedrock
        from app.llm.bedrock import BedrockProcessor
        
        print("✅ Imports successful!")
        
        # Create a mock BedrockProcessor to avoid actual AWS calls
        mock_bedrock_processor = Mock(spec=BedrockProcessor)
        
        # Mock the call_bedrock_converse method
        def mock_call_bedrock_converse(*args, **kwargs):
            print(f"\n🔧 MOCK: call_bedrock_converse called")
            print(f"   Args: {len(args)} arguments")
            print(f"   Kwargs keys: {list(kwargs.keys())}")
            
            # Check if tool_config is in kwargs
            if 'tool_config' in kwargs:
                tool_config = kwargs['tool_config']
                print(f"   ✅ tool_config found in kwargs")
                print(f"   tool_config type: {type(tool_config)}")
                if isinstance(tool_config, dict) and 'tools' in tool_config:
                    print(f"   tool_config has 'tools' key with {len(tool_config['tools'])} tools")
                else:
                    print(f"   ❌ tool_config structure issue: {tool_config}")
            else:
                print(f"   ❌ tool_config NOT found in kwargs")
            
            # Return a mock response
            return {
                "stopReason": "tool_use",
                "output": {
                    "message": {
                        "content": [
                            {"text": "Classification complete"},
                            {
                                "toolUse": {
                                    "input": {
                                        "documents": [
                                            {"page_no": 1, "doc_type": "invoice"}
                                        ]
                                    }
                                }
                            }
                        ]
                    }
                }
            }
        
        # Mock the extract_tool_response method
        def mock_extract_tool_response(response):
            return response['output']['message']['content'][1]['toolUse']['input']
        
        mock_bedrock_processor.call_bedrock_converse = mock_call_bedrock_converse
        mock_bedrock_processor.extract_tool_response = mock_extract_tool_response
        
        # Test with sample document text
        sample_text = """<page1>
INVOICE #INV-2024-001

Bill To:
ABC Company
123 Main Street
City, State 12345

Invoice Date: 2024-01-15
Due Date: 2024-02-15

Description: Professional Services
Amount: $1,500.00
Tax: $150.00
Total Due: $1,650.00
</page1>"""
        
        print(f"\n📄 Testing with sample invoice text...")
        print(f"Text length: {len(sample_text)} characters")
        
        # Call the function - this should trigger our debug logging
        result = await get_document_types_with_bedrock(mock_bedrock_processor, sample_text)
        
        print(f"\n✅ Function completed successfully!")
        print(f"Result: {result[0]}")  # First element is the content
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

def main():
    """Main function to run the test"""
    print("Starting debug test for TOOL_CALL_CLASSIFICATION...")
    
    # Run the async test
    success = asyncio.run(test_classification_with_debug())
    
    if success:
        print(f"\n🎯 Test completed! Check the debug logs above to see:")
        print(f"   1. Whether TOOL_CALL_CLASSIFICATION is properly imported")
        print(f"   2. Whether it's being passed to call_bedrock_converse")
        print(f"   3. Whether it's being added to the Bedrock call parameters")
    else:
        print(f"\n❌ Test failed - check the error messages above")

if __name__ == "__main__":
    main()
