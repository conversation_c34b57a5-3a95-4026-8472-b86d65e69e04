"""
AWS Bedrock integration for AI-powered data extraction/classification.

This module provides AWS Bedrock client support for:
- Initializing the BedrockProcessor with AWS credentials and region settings.
- Facilitating conversation with AWS Bedrock service via the converse API.
- Extracting text and tool responses from Bedrock API replies.
- Comprehensive logging and error handling for AWS interactions.
"""

import boto3
import logging
import traceback
from typing import Dict, Optional, List
from app.core.configuration import settings

# Configure logging
logger = logging.getLogger(__name__)


class BedrockProcessor:
    """
    AWS Bedrock processor for AI-powered data extraction/classification.
    """
    
    def __init__(self, aws_region: Optional[str] = None, aws_access_key_id: Optional[str] = None, aws_secret_access_key: Optional[str] = None):
        """
        Initialize Bedrock processor with credentials from settings.

        Args:
            aws_region: AWS region (defaults to settings)
            aws_access_key_id: AWS access key ID (defaults to settings)
            aws_secret_access_key: AWS secret access key (defaults to settings)
        """
        logger.info("Initializing BedrockProcessor...")

        try:
            # Load AWS credentials from settings if not provided
            aws_region = aws_region or settings.AWS_REGION_EXTRACTION
            aws_access_key_id = aws_access_key_id or settings.AWS_ACCESS_KEY_ID
            aws_secret_access_key = aws_secret_access_key or settings.AWS_SECRET_ACCESS_KEY

            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                region_name=aws_region,
                aws_access_key_id=aws_access_key_id,
                aws_secret_access_key=aws_secret_access_key
            )

            self.region = aws_region

        except Exception as e:
            logger.error(f"Failed to initialize BedrockProcessor: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise

    def call_bedrock_converse(self, model_id: str, system_prompt: str, messages: List[Dict],
                              inference_config: Dict, tool_config: Optional[Dict] = None, additionalModelRequestFields: Optional[Dict] = None) -> Dict:
        """
        Helper method to call Bedrock converse API with common configuration.

        Args:
            model_id: Bedrock model ID
            system_prompt: System prompt text
            messages: List of message dictionaries
            inference_config: Inference configuration dictionary
            tool_config: Optional tool configuration for structured output
            additionalModelRequestFields: Optional additional model request fields

        Returns:
            Bedrock response dictionary
        """
        call_params = {
            'modelId': model_id,
            'system': [{"text": system_prompt}],
            'messages': messages,
            'inferenceConfig': inference_config
        }

        # DEBUG: Log tool_config parameter details
        logger.info("=== DEBUG: BEDROCK CALL_PARAMS ===")
        logger.info(f"tool_config parameter type: {type(tool_config)}")
        logger.info(f"tool_config is None: {tool_config is None}")
        logger.info(f"tool_config is truthy: {bool(tool_config)}")

        if tool_config:
            call_params['toolConfig'] = tool_config
            logger.info("✅ tool_config added to call_params as 'toolConfig'")
            logger.info(f"toolConfig keys: {list(tool_config.keys()) if isinstance(tool_config, dict) else 'Not a dict'}")
        else:
            logger.info("❌ tool_config is falsy - NOT added to call_params")
            logger.info(f"tool_config value: {tool_config}")

        if additionalModelRequestFields:
            call_params['additionalModelRequestFields'] = additionalModelRequestFields

        logger.info(f"Final call_params keys: {list(call_params.keys())}")
        logger.info("=== END BEDROCK DEBUG ===")

        return self.bedrock_client.converse(**call_params)

    def extract_text_from_response(self, response: Dict) -> str:
        """
        Helper method to extract text content from Bedrock response.

        Args:
            response: Bedrock response dictionary

        Returns:
            Extracted text content

        Raises:
            Exception: If response format is unexpected
        """
        try:
            if 'output' in response and 'message' in response['output']:
                content = response['output']['message']['content']
                if len(content) > 0 and 'text' in content[0]:
                    return content[0]['text']
        except Exception as e:
            logger.error(f"Failed to extract text from response: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
        raise Exception("Unexpected response format from Bedrock")

    def extract_tool_response(self, response: Dict) -> Dict:
        """
        Helper method to extract tool use response from Bedrock.

        Args:
            response: Bedrock response dictionary

        Returns:
            Tool response content

        Raises:
            Exception: If tool response format is unexpected
        """
        try:
            # if stop reason is tooluse
            if response['stopReason'] == 'tool_use':
                content = response['output']['message']['content'][1]['toolUse']['input']
                return content
            else:
                raise Exception("Stop reason is not tool_use")
        except Exception as e:
            logger.error(f"Failed to extract tool response: {str(e)}")
            logger.debug(f"Traceback: {traceback.format_exc()}")
            raise Exception("Unexpected tool response format from Bedrock")


